import React, { useState } from "react";

export default function BTCLoanCalculator() {
  const [startingBTC, setStartingBTC] = useState(2);
  const [annualWithdrawal, setAnnualWithdrawal] = useState(20000);
  const [rollInCosts, setRollInCosts] = useState(false);
  const [loanAPR, setLoanAPR] = useState(10);
  const [ltv, setLTV] = useState(0.5);
  const [btcPrices, setBtcPrices] = useState([
    106000, 140000, 180000, 200000, 230000, 260000, 290000,
    320000, 350000, 380000, 410000, 440000, 470000, 500000,
    530000, 560000, 590000, 620000, 650000, 680000, 710000,
    740000, 770000, 800000, 830000, 860000, 890000, 920000,
    950000, 980000, 1010000, 1040000, 1070000, 1100000
  ]);

  const [results, setResults] = useState([]);

  const calculate = () => {
    let btcLeft = startingBTC;
    const output = [];

    for (let i = 0; i < btcPrices.length; i++) {
      const price = btcPrices[i];
      const cumulativeWithdrawal = annualWithdrawal * (i + 1);
      const interest = cumulativeWithdrawal * (loanAPR / 100);
      const originationFee = cumulativeWithdrawal * 0.02;
      const totalLoanCosts = interest + originationFee;
      const totalLoan = rollInCosts ? cumulativeWithdrawal + totalLoanCosts : cumulativeWithdrawal;

      const btcNeeded = totalLoan / (price * ltv);
      if (btcLeft < btcNeeded) break;

      btcLeft = startingBTC - btcNeeded;

      output.push({
        year: i + 1,
        btcPrice: price,
        btcUsed: btcNeeded.toFixed(3),
        unlockedBTC: btcLeft.toFixed(3),
        btcLocked: btcNeeded.toFixed(3),
        annualLoanCost: totalLoanCosts.toFixed(2),
        loanCosts: rollInCosts ? null : totalLoanCosts.toFixed(2),
        totalCost: totalLoan.toFixed(2),
        unlockedValue: btcLeft
      });
    }

    setResults(output);
  };

  return (
    <div className="p-4 max-w-md mx-auto">
      <h1 className="text-xl font-bold mb-2 text-center">Borrow from BTC 💰</h1>

      <div className="space-y-3">
        <div className="flex flex-col w-full">
          <label className="text-sm font-medium mb-1">Total BTC</label>
          <input
            className="w-1/2"
            type="number"
            step="0.01"
            value={startingBTC}
            onChange={(e) => setStartingBTC(parseFloat(e.target.value))}
          />
        </div>

        <div className="flex flex-col w-full">
          <label className="text-sm font-medium mb-1">USD Annual Withdrawal</label>
          <input
            className="w-1/2"
            type="number"
            value={annualWithdrawal}
            onChange={(e) => setAnnualWithdrawal(parseFloat(e.target.value))}
          />
        </div>

        <div className="flex flex-col w-full">
          <label className="text-sm font-medium mb-1">Loan APR (%)</label>
          <input
            className="w-1/2"
            type="number"
            step="0.1"
            value={loanAPR}
            onChange={(e) => setLoanAPR(parseFloat(e.target.value))}
          />
        </div>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={rollInCosts}
            onChange={(e) => setRollInCosts(e.target.checked)}
          />
          <span>Roll in loan costs</span>
        </label>

        <label className="block">LTV Ratio (%): {Math.round(ltv * 100)}</label>
        <input
          type="range"
          min="0.3"
          max="0.8"
          step="0.01"
          value={ltv}
          onChange={(e) => setLTV(parseFloat(e.target.value))}
        />

        <button
          onClick={calculate}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
        >
          Calculate
        </button>
      </div>

      {results.length > 0 && (
        <div className="mt-6 space-y-4">
          {results.map((r, idx) => {
            let bgColor = "bg-green-100 text-green-700";
            if (r.unlockedValue < 0.5) bgColor = "bg-red-100 text-red-700";
            else if (r.unlockedValue < 1) bgColor = "bg-orange-100 text-orange-700";

            return (
              <div key={r.year} className="p-4 border rounded shadow bg-white">
                <div className="flex items-center justify-between">
                  <strong>Year {r.year}</strong>
                  {idx === 0 && (
                    <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">Starting Loan</span>
                  )}
                  {idx === results.length - 1 && (
                    <span className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded ml-2">Final BTC Loan</span>
                  )}
                </div>
                <div>BTC Price: ${r.btcPrice.toLocaleString()}</div>
                <div>BTC Used: {r.btcUsed} BTC</div>
                <div>BTC Locked: {r.btcLocked} BTC</div>
                <div>New Loan Costs: ${r.annualLoanCost}</div>
                {r.loanCosts && (
                  <div>Loan Costs: ${r.loanCosts}</div>
                )}
                <div>Total Repay: ${r.totalCost}</div>
                <div className={`mt-2 ${bgColor} px-3 py-2 font-bold text-lg rounded shadow ring-2 ring-green-400 animate-pulse`}>
                  Liquid BTC Remaining: {r.unlockedBTC} BTC
                </div>
              </div>
            );
          })}

          <div className="btc-chart-table mt-8">
            <table className="text-xs w-full border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border p-1">Year</th>
                  <th className="border p-1">BTC Price</th>
                  <th className="border p-1">BTC Used</th>
                  <th className="border p-1">BTC Locked</th>
                  <th className="border p-1">New Loan Costs</th>
                  {rollInCosts ? <th className="border p-1">New Borrow Amount</th> : <th className="border p-1">Loan Costs</th>}
                  <th className="border p-1">Total Repay</th>
                  <th className="border p-1">BTC Remaining</th>
                </tr>
              </thead>
              <tbody>
                {results.map(r => (
                  <tr key={r.year}>
                    <td className="border p-1">{r.year}</td>
                    <td className="border p-1">${r.btcPrice.toLocaleString()}</td>
                    <td className="border p-1">{r.btcUsed}</td>
                    <td className="border p-1">{r.btcLocked}</td>
                    <td className="border p-1">{r.annualLoanCost}</td>
                    {rollInCosts ? <td className="border p-1">{r.totalCost}</td> : <td className="border p-1">{r.loanCosts}</td>}
                    <td className="border p-1">{r.totalCost}</td>
                    <td className="border p-1">{r.unlockedBTC}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-10 text-xs text-center text-gray-500 border-t pt-4">
            <p>Assumes a 2% loan origination fee.</p>
            <p>This tool is for illustrative purposes only and does not constitute financial advice. No guarantees are made regarding accuracy or future performance.</p>
            <p className="mt-2">Built by <a href="https://twitter.com/punksgan" className="text-blue-500 hover:underline">Snar</a></p>
          </div>
        </div>
      )}
    </div>
  );
}
