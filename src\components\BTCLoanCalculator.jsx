import React, { useState, useEffect } from "react";

export default function BTCLoanCalculator() {
  const [startingBTC, setStartingBTC] = useState(2);
  const [annualWithdrawal, setAnnualWithdrawal] = useState(20000);
  const [rollInCosts, setRollInCosts] = useState(false);
  const [loanAPR, setLoanAPR] = useState(10);
  const [ltv, setLTV] = useState(0.5);
  const [btcPrices, setBtcPrices] = useState([]);
  const [results, setResults] = useState([]);

  useEffect(() => {
    fetch("https://min-api.cryptocompare.com/data/generateAvg?fsym=BTC&tsym=USD&e=coinbase")
      .then((res) => res.json())
      .then((data) => {
        const startPrice = data?.RAW?.PRICE || 106000;
        const startYear = 2025 - 2009;
        const exponent = 5.0;
        const scalingFactor = startPrice / Math.pow(startYear, exponent);
        const projectedPrices = Array.from({ length: 30 }, (_, i) =>
          Math.round(scalingFactor * Math.pow(startYear + i, exponent))
        );
        console.log("BTC Projections:", projectedPrices);
        setBtcPrices(projectedPrices);
      })
      .catch(() => {
        const fallbackPrices = [
          106000, 140000, 180000, 200000, 230000, 260000, 290000,
          320000, 350000, 380000, 410000, 440000, 470000, 500000,
          530000, 560000, 590000, 620000, 650000, 680000, 710000,
          740000, 770000, 800000, 830000, 860000, 890000, 920000,
          950000, 980000
        ];
        console.log("Fallback BTC Projections:", fallbackPrices);
        setBtcPrices(fallbackPrices);
      });
  }, []);

  const calculate = () => {
    let btcLeft = startingBTC;
    const output = [];

    for (let i = 0; i < btcPrices.length; i++) {
      const price = btcPrices[i];
      const year = i + 1;
      const newLoan = annualWithdrawal * year;
      const interest = newLoan * (loanAPR / 100);
      const originationFee = newLoan * 0.02;
      const loanCost = interest + originationFee;
      const totalRepay = rollInCosts ? newLoan + loanCost : newLoan;
      const btcNeeded = totalRepay / (price * ltv);

      if (btcLeft < btcNeeded) break;
      btcLeft -= btcNeeded;

      output.push({
        year,
        btcPrice: price,
        btcLocked: btcNeeded.toFixed(3),
        unlockedBTC: btcLeft.toFixed(3),
        loanCosts: loanCost.toFixed(2),
        annualLoanCost: (newLoan * (loanAPR / 100 + 0.02)).toFixed(2),
        totalCost: totalRepay.toFixed(2),
        unlockedValue: btcLeft
      });
    }

    setResults(output);
  };

  return (
    <div className="p-4 max-w-md mx-auto">
      <p className="text-sm text-gray-600 text-center mb-2">
        Estimate how long your BTC can support annual cash withdrawals under a refinancing loan model.
      </p>
      <h1 className="text-xl font-bold mb-2 text-center">Borrow from BTC 💰</h1>

      <div className="space-y-3">
        <div className="flex flex-col w-full">
          <label htmlFor="btc" className="text-sm font-medium mb-1">Total BTC</label>
          <input
            id="btc"
            className="w-1/2"
            type="number"
            step="0.01"
            value={startingBTC}
            onChange={(e) => setStartingBTC(parseFloat(e.target.value))}
          />
        </div>

        <div className="flex flex-col w-full">
          <label htmlFor="withdrawal" className="text-sm font-medium mb-1">Annual USD Withdrawal</label>
          <input
            id="withdrawal"
            className="w-1/2"
            type="number"
            value={annualWithdrawal}
            onChange={(e) => setAnnualWithdrawal(parseFloat(e.target.value))}
          />
        </div>

        <div className="flex flex-col w-full">
          <label htmlFor="apr" className="text-sm font-medium mb-1" title="Annual interest rate charged on the loan">Loan APR (%)</label>
          <input
            id="apr"
            className="w-1/2"
            type="number"
            step="0.1"
            value={loanAPR}
            onChange={(e) => setLoanAPR(parseFloat(e.target.value))}
          />
        </div>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={rollInCosts}
            onChange={(e) => setRollInCosts(e.target.checked)}
          />
          <span title="Include loan fees in the borrowed amount instead of paying them upfront">Roll in loan costs</span>
        </label>

        <label className="block">LTV Ratio (%): {Math.round(ltv * 100)}</label>
        <input
          type="range"
          min="0.3"
          max="0.8"
          step="0.01"
          value={ltv}
          onChange={(e) => setLTV(parseFloat(e.target.value))}
        />

        <button
          onClick={calculate}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
        >
          Calculate
        </button>
      </div>
    </div>
  );
}
