import React, { useState, useEffect } from "react";

export default function BTCLoanCalculator() {
  const [startingBTC, setStartingBTC] = useState(2);
  const [annualWithdrawal, setAnnualWithdrawal] = useState(20000);
  const [rollInCosts, setRollInCosts] = useState(false);
  const [loanAPR, setLoanAPR] = useState(10);
  const [ltv, setLTV] = useState(0.5);
  const [usePowerLaw, setUsePowerLaw] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [btcPrices, setBtcPrices] = useState([
    106000, 140000, 180000, 200000, 230000, 260000, 290000,
    320000, 350000, 380000, 410000, 440000, 470000, 500000,
    530000, 560000, 590000, 620000, 650000, 680000, 710000,
    740000, 770000, 800000, 830000, 860000, 890000, 920000,
    950000, 980000, 1010000, 1040000, 1070000, 1100000
  ]);

  // Conservative BTC prices (current default)
  const conservativePrices = [
    106000, 140000, 180000, 200000, 230000, 260000, 290000,
    320000, 350000, 380000, 410000, 440000, 470000, 500000,
    530000, 560000, 590000, 620000, 650000, 680000, 710000,
    740000, 770000, 800000, 830000, 860000, 890000, 920000,
    950000, 980000, 1010000, 1040000, 1070000, 1100000
  ];

  // Power Law Model - fetch current price and generate projections
  useEffect(() => {
    if (usePowerLaw) {
      fetch("https://min-api.cryptocompare.com/data/generateAvg?fsym=BTC&tsym=USD&e=coinbase")
        .then((res) => res.json())
        .then((data) => {
          const currentPrice = data?.RAW?.PRICE || 106000;

          // Bitcoin Power Law: Price = A * (days since genesis)^n
          // Genesis: January 3, 2009
          // Current approach: calibrate the model to current price and project forward
          const genesisDate = new Date('2009-01-03');
          const currentDate = new Date();
          const currentDaysSinceGenesis = Math.floor((currentDate - genesisDate) / (1000 * 60 * 60 * 24));

          // Calculate coefficient A based on current price
          // Using exponent of 5.8 (commonly cited in power law literature)
          const exponent = 5.8;
          const A = currentPrice / Math.pow(currentDaysSinceGenesis, exponent);

          const projectedPrices = Array.from({ length: 30 }, (_, i) => {
            const futureDate = new Date(currentDate);
            futureDate.setFullYear(futureDate.getFullYear() + i);
            const daysSinceGenesis = Math.floor((futureDate - genesisDate) / (1000 * 60 * 60 * 24));
            const powerLawPrice = A * Math.pow(daysSinceGenesis, exponent);
            return Math.round(powerLawPrice);
          });

          setBtcPrices(projectedPrices);
        })
        .catch(() => {
          // fallback to power law with default calculation if API fails
          const currentPrice = 106000;
          const genesisDate = new Date('2009-01-03');
          const currentDate = new Date();
          const currentDaysSinceGenesis = Math.floor((currentDate - genesisDate) / (1000 * 60 * 60 * 24));

          const exponent = 5.8;
          const A = currentPrice / Math.pow(currentDaysSinceGenesis, exponent);

          const projectedPrices = Array.from({ length: 30 }, (_, i) => {
            const futureDate = new Date(currentDate);
            futureDate.setFullYear(futureDate.getFullYear() + i);
            const daysSinceGenesis = Math.floor((futureDate - genesisDate) / (1000 * 60 * 60 * 24));
            const powerLawPrice = A * Math.pow(daysSinceGenesis, exponent);
            return Math.round(powerLawPrice);
          });

          setBtcPrices(projectedPrices);
        });
    } else {
      // Use conservative prices
      setBtcPrices(conservativePrices);
    }
  }, [usePowerLaw]);

  const [results, setResults] = useState([]);
  const [conservativeResults, setConservativeResults] = useState([]);
  const [powerLawResults, setPowerLawResults] = useState([]);

  // Helper function to calculate results for a given price array
  const calculateWithPrices = (prices) => {
    let btcLeft = startingBTC;
    const output = [];

    for (let i = 0; i < prices.length; i++) {
      const price = prices[i];
      const cumulativeWithdrawal = annualWithdrawal * (i + 1);
      const interest = cumulativeWithdrawal * (loanAPR / 100);
      const originationFee = cumulativeWithdrawal * 0.02;
      const totalLoanCosts = interest + originationFee;
      const totalLoan = rollInCosts ? cumulativeWithdrawal + totalLoanCosts : cumulativeWithdrawal;

      const btcNeeded = totalLoan / (price * ltv);
      if (btcLeft < btcNeeded) break;

      btcLeft = startingBTC - btcNeeded;

      output.push({
        year: i + 1,
        btcPrice: price,
        unlockedBTC: btcLeft.toFixed(3),
        btcLocked: btcNeeded.toFixed(3),
        annualLoanCost: rollInCosts ? totalLoanCosts.toFixed(2) : null,
        loanCosts: !rollInCosts ? totalLoanCosts.toFixed(2) : null,
        totalCost: totalLoan.toFixed(2),
        unlockedValue: btcLeft
      });
    }

    return output;
  };

  const calculate = () => {
    if (showComparison) {
      // Calculate both models for comparison
      const conservativeOutput = calculateWithPrices(conservativePrices);
      const powerLawOutput = calculateWithPrices(btcPrices); // btcPrices will be power law prices

      setConservativeResults(conservativeOutput);
      setPowerLawResults(powerLawOutput);
      setResults([]); // Clear single results
    } else {
      // Calculate single model
      const output = calculateWithPrices(btcPrices);
      setResults(output);
      setConservativeResults([]);
      setPowerLawResults([]);
    }
  };

  // Helper function to render summary cards
  const renderSummaryCards = (results, title, colorScheme) => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-center">{title}</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <div className={`summary-card bg-gradient-to-br ${colorScheme.duration} border p-4 rounded-lg shadow-sm`}>
          <h4 className="font-medium text-sm mb-1">Duration</h4>
          <div className="text-2xl font-bold">{results.length} Years</div>
        </div>

        <div className={`summary-card bg-gradient-to-br ${colorScheme.btc} border p-4 rounded-lg shadow-sm`}>
          <h4 className="font-medium text-sm mb-1">BTC Remaining</h4>
          <div className="text-2xl font-bold">{results[results.length - 1]?.unlockedBTC} BTC</div>
        </div>

        <div className={`summary-card bg-gradient-to-br ${colorScheme.income} border p-4 rounded-lg shadow-sm`}>
          <h4 className="font-medium text-sm mb-1">Total Income</h4>
          <div className="text-2xl font-bold">${(annualWithdrawal * results.length).toLocaleString()}</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-4 max-w-4xl mx-auto">
      {/* Hero Section */}
      <div className="hero-section mb-8 text-center">
        <h1 className="text-3xl font-bold mb-3">
          How Long Can Your Bitcoin Last? 💰
        </h1>
        <p className="text-lg text-gray-600 mb-4 max-w-2xl mx-auto">
          Model sustainable income from your BTC using refinancing loans. Keep your Bitcoin while accessing cash flow.
        </p>
        <div className="flex flex-wrap justify-center gap-3 text-sm">
          <span className="bg-green-100 text-green-800 px-3 py-2 rounded-full font-medium">
            ✅ Keep Your BTC
          </span>
          <span className="bg-blue-100 text-blue-800 px-3 py-2 rounded-full font-medium">
            📈 Two Price Models
          </span>
          <span className="bg-orange-100 text-orange-800 px-3 py-2 rounded-full font-medium">
            🔒 Private & Secure
          </span>
        </div>
      </div>

      {/* Calculator Section */}
      <div className="max-w-md mx-auto">

      {/* Scenario Presets */}
      <div className="scenario-presets mb-6">
        <h3 className="text-lg font-semibold mb-3 text-center">Quick Start Scenarios</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          <button
            onClick={() => {
              setStartingBTC(1);
              setAnnualWithdrawal(15000);
              setLoanAPR(10);
              setLTV(0.5);
              setRollInCosts(false);
              setUsePowerLaw(false);
            }}
            className="preset-button bg-blue-50 hover:bg-blue-100 border border-blue-200 p-4 rounded-lg transition-colors"
          >
            <div className="font-semibold text-blue-800">Conservative Retiree</div>
            <div className="text-sm text-blue-600 mt-1">1 BTC • $15k/year</div>
            <div className="text-xs text-blue-500 mt-1">Safe & steady approach</div>
          </button>

          <button
            onClick={() => {
              setStartingBTC(5);
              setAnnualWithdrawal(50000);
              setLoanAPR(10);
              setLTV(0.7);
              setRollInCosts(true);
              setUsePowerLaw(true);
            }}
            className="preset-button bg-orange-50 hover:bg-orange-100 border border-orange-200 p-4 rounded-lg transition-colors"
          >
            <div className="font-semibold text-orange-800">Aggressive Investor</div>
            <div className="text-sm text-orange-600 mt-1">5 BTC • $50k/year</div>
            <div className="text-xs text-orange-500 mt-1">High income, power law</div>
          </button>

          <button
            onClick={() => {
              setStartingBTC(0.5);
              setAnnualWithdrawal(10000);
              setLoanAPR(10);
              setLTV(0.4);
              setRollInCosts(false);
              setUsePowerLaw(true);
            }}
            className="preset-button bg-green-50 hover:bg-green-100 border border-green-200 p-4 rounded-lg transition-colors"
          >
            <div className="font-semibold text-green-800">Young HODLer</div>
            <div className="text-sm text-green-600 mt-1">0.5 BTC • $10k/year</div>
            <div className="text-xs text-green-500 mt-1">Small stack, big dreams</div>
          </button>
        </div>
        <p className="text-xs text-gray-500 text-center mt-3">
          Click a scenario to auto-fill the calculator, then adjust as needed
        </p>
      </div>

      <div className="space-y-3">
        <div className="flex flex-col w-full">
          <label htmlFor="btc" className="text-sm font-medium mb-1">Total BTC</label>
          <input
            id="btc"
            className="w-1/2"
            type="number"
            step="0.01"
            value={startingBTC}
            onChange={(e) => setStartingBTC(parseFloat(e.target.value))}
          />
        </div>

        <div className="flex flex-col w-full">
          <label htmlFor="withdrawal" className="text-sm font-medium mb-1">Annual USD Withdrawal</label>
          <input
            id="withdrawal"
            className="w-1/2"
            type="number"
            value={annualWithdrawal}
            onChange={(e) => setAnnualWithdrawal(parseFloat(e.target.value))}
          />
        </div>

        <div className="flex flex-col w-full">
          <label htmlFor="apr" className="text-sm font-medium mb-1" title="Annual interest rate charged on the loan">Loan APR (%)</label>
          <input
            id="apr"
            className="w-1/2"
            type="number"
            step="0.1"
            value={loanAPR}
            onChange={(e) => setLoanAPR(parseFloat(e.target.value))}
          />
        </div>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={rollInCosts}
            onChange={(e) => setRollInCosts(e.target.checked)}
          />
          <span title="Include loan fees in the borrowed amount instead of paying them upfront">Roll in loan costs</span>
        </label>

        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">BTC Price Model</label>
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="priceModel"
                checked={!usePowerLaw}
                onChange={() => setUsePowerLaw(false)}
              />
              <span>Conservative</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="priceModel"
                checked={usePowerLaw}
                onChange={() => setUsePowerLaw(true)}
              />
              <span>Power Law Model</span>
            </label>
          </div>
        </div>

        <label className="block">LTV Ratio (%): {Math.round(ltv * 100)}</label>
        <input
          type="range"
          min="0.3"
          max="0.8"
          step="0.01"
          value={ltv}
          onChange={(e) => setLTV(parseFloat(e.target.value))}
        />

        <div className="flex flex-col space-y-3">
          <div className="flex items-center justify-center space-x-4">
            <button
              onClick={calculate}
              className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition font-medium"
            >
              Calculate
            </button>

            <button
              onClick={() => {
                setShowComparison(!showComparison);
                if (!showComparison) {
                  // When enabling comparison, force power law prices to be calculated
                  setUsePowerLaw(true);
                }
              }}
              className={`px-4 py-2 rounded border transition font-medium ${
                showComparison
                  ? 'bg-orange-100 border-orange-300 text-orange-800 hover:bg-orange-200'
                  : 'bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100'
              }`}
            >
              {showComparison ? '📊 Exit Comparison' : '⚡ Compare Models'}
            </button>
          </div>

          {showComparison && (
            <div className="text-center text-sm text-orange-600 bg-orange-50 p-3 rounded border border-orange-200">
              <strong>Comparison Mode:</strong> See Conservative vs Power Law side-by-side
            </div>
          )}
        </div>
      </div>
      </div>

      {/* Comparison Mode Results */}
      {showComparison && (conservativeResults.length > 0 || powerLawResults.length > 0) && (
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-center mb-6">Conservative vs Power Law Comparison</h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Conservative Results */}
            {conservativeResults.length > 0 && (
              <div className="conservative-comparison border-2 border-blue-200 rounded-xl p-6 bg-blue-50">
                {renderSummaryCards(conservativeResults, "🔵 Conservative Model", {
                  duration: "from-blue-100 to-blue-200 border-blue-300 text-blue-800",
                  btc: "from-blue-100 to-blue-200 border-blue-300 text-blue-800",
                  income: "from-blue-100 to-blue-200 border-blue-300 text-blue-800"
                })}
              </div>
            )}

            {/* Power Law Results */}
            {powerLawResults.length > 0 && (
              <div className="powerlaw-comparison border-2 border-orange-200 rounded-xl p-6 bg-orange-50">
                {renderSummaryCards(powerLawResults, "🚀 Power Law Model", {
                  duration: "from-orange-100 to-orange-200 border-orange-300 text-orange-800",
                  btc: "from-orange-100 to-orange-200 border-orange-300 text-orange-800",
                  income: "from-orange-100 to-orange-200 border-orange-300 text-orange-800"
                })}
              </div>
            )}
          </div>

          {/* Comparison Insights */}
          {conservativeResults.length > 0 && powerLawResults.length > 0 && (
            <div className="comparison-insights mt-6 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-purple-800 mb-3 text-center">📈 The Power Law Advantage</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    +{powerLawResults.length - conservativeResults.length} Years
                  </div>
                  <div className="text-sm text-purple-700">Extra Duration</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    +{((parseFloat(powerLawResults[powerLawResults.length - 1]?.unlockedBTC) - parseFloat(conservativeResults[conservativeResults.length - 1]?.unlockedBTC)) || 0).toFixed(2)} BTC
                  </div>
                  <div className="text-sm text-purple-700">More BTC Remaining</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    +${((powerLawResults.length - conservativeResults.length) * annualWithdrawal).toLocaleString()}
                  </div>
                  <div className="text-sm text-purple-700">Extra Income</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Single Model Results */}
      {!showComparison && results.length > 0 && (
        <div className="mt-8">
          {/* Results Summary Cards */}
          <div className="results-summary grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="summary-card bg-gradient-to-br from-green-50 to-green-100 border border-green-200 p-6 rounded-xl shadow-sm">
              <h3 className="font-semibold text-green-800 mb-2">Sustainable Duration</h3>
              <div className="text-3xl font-bold text-green-600">{results.length} Years</div>
              <p className="text-sm text-green-700 mt-1">
                {results.length >= 20 ? "Excellent long-term strategy!" :
                 results.length >= 10 ? "Good medium-term plan" :
                 "Consider adjusting parameters"}
              </p>
            </div>

            <div className="summary-card bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 p-6 rounded-xl shadow-sm">
              <h3 className="font-semibold text-blue-800 mb-2">Final BTC Remaining</h3>
              <div className="text-3xl font-bold text-blue-600">{results[results.length - 1]?.unlockedBTC} BTC</div>
              <p className="text-sm text-blue-700 mt-1">
                {parseFloat(results[results.length - 1]?.unlockedBTC) > 1 ?
                 "Strong BTC preservation" :
                 "Most BTC utilized"}
              </p>
            </div>

            <div className="summary-card bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 p-6 rounded-xl shadow-sm">
              <h3 className="font-semibold text-orange-800 mb-2">Total Income Generated</h3>
              <div className="text-3xl font-bold text-orange-600">${(annualWithdrawal * results.length).toLocaleString()}</div>
              <p className="text-sm text-orange-700 mt-1">
                Over {results.length} years
              </p>
            </div>
          </div>

          {/* Detailed Results */}
          <div className="detailed-results">
            <h2 className="text-xl font-semibold mb-4 text-center">Year-by-Year Breakdown</h2>
            <div className="space-y-4">
            {results.map((r, idx) => {
            let bgColor = "bg-green-100 text-green-700";
            if (r.unlockedValue < 0.5) bgColor = "bg-red-100 text-red-700";
            else if (r.unlockedValue < 1) bgColor = "bg-orange-100 text-orange-700";

            return (
              <div key={r.year} className="p-4 border rounded shadow bg-white">
                <div className="flex items-center justify-between">
                  <strong>Year {r.year}</strong>
                  {idx === 0 && (
                    <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">Starting Loan</span>
                  )}
                  {idx === results.length - 1 && (
                    <span className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded ml-2">Final BTC Loan</span>
                  )}
                </div>
                <div>BTC Price: ${r.btcPrice.toLocaleString()}</div>
                <div>BTC Locked: {r.btcLocked} BTC</div>
                {rollInCosts ? (
                  <div>New Loan Costs: ${r.annualLoanCost}</div>
                ) : (
                  <div>Upfront Loan Costs: ${r.loanCosts}</div>
                )}
                <div>Total Repay: ${r.totalCost}</div>
                <div className={`mt-2 ${bgColor} px-3 py-2 font-bold text-lg rounded shadow ring-2 ring-green-400 animate-pulse`}>
                  Liquid BTC Remaining: {r.unlockedBTC} BTC
                </div>
              </div>
            );
          })}

          <div className="btc-chart-table mt-8">
            <table className="text-xs w-full border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border p-1">Year</th>
                  <th className="border p-1">BTC Price</th>
                  <th className="border p-1">BTC Locked</th>
                  {rollInCosts ? (
                    <th className="border p-1">New Loan Costs</th>
                  ) : (
                    <th className="border p-1">Upfront Loan Costs</th>
                  )}
                  {rollInCosts ? (
                    <th className="border p-1">New Borrow Amount</th>
                  ) : null}
                  <th className="border p-1">Total Repay</th>
                  <th className="border p-1">BTC Remaining</th>
                </tr>
              </thead>
              <tbody>
                {results.map(r => (
                  <tr key={r.year}>
                    <td className="border p-1">{r.year}</td>
                    <td className="border p-1">${r.btcPrice.toLocaleString()}</td>
                    <td className="border p-1">{r.btcLocked}</td>
                    <td className="border p-1">{rollInCosts ? r.annualLoanCost : r.loanCosts}</td>
                    {rollInCosts && (
                      <td className="border p-1">{r.totalCost}</td>
                    )}
                    <td className="border p-1">{r.totalCost}</td>
                    <td className="border p-1">{r.unlockedBTC}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-10 text-xs text-center text-gray-500 border-t pt-4">
            <p>Assumes a 2% loan origination fee and annual refinancing of entire owed USD amount.</p>
            <p>This tool is for illustrative purposes only and does not constitute financial advice. No guarantees are made regarding accuracy or future performance.</p>
            <p className="mt-2">Built by <a href="https://twitter.com/punksgan" className="text-blue-500 hover:underline">Snar</a></p>
          </div>
          </div>
          </div>
        </div>
      )}
    </div>
  );
}
